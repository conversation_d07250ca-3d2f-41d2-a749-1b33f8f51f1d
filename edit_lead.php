<?php
// Include database connection and functions
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!is_logged_in()) {
    header("Location: auth/login.php");
    exit;
}

// Get lead ID from URL
$lead_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($lead_id <= 0) {
    header("Location: leads.php?error=1&message=" . urlencode("Invalid lead ID"));
    exit;
}

// Check if user has permission to edit this lead
$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Get lead data
$sql = "SELECT * FROM leads WHERE id = ?";
if ($is_sales_rep) {
    $sql .= " AND assigned_to = ?";
}

$stmt = mysqli_prepare($conn, $sql);
if ($is_sales_rep) {
    mysqli_stmt_bind_param($stmt, "ii", $lead_id, $sales_rep_id);
} else {
    mysqli_stmt_bind_param($stmt, "i", $lead_id);
}
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: leads.php?error=1&message=" . urlencode("Lead not found or access denied"));
    exit;
}

$lead = mysqli_fetch_assoc($result);

// Initialize variables with existing data
$first_name = $lead['first_name'];
$last_name = $lead['last_name'];
$email = $lead['email'];
$phone = $lead['phone'];
$country_code = $lead['country_code'] ?? '+91';
$interest = $lead['customer_interest'];
$source = $lead['source'];
$notes = $lead['notes'];
$assigned_to = $lead['assigned_to'];
$status = $lead['status'];

$first_name_err = $last_name_err = $email_err = $phone_err = $interest_err = "";
$success_message = $error_message = "";

// Get all products for the interest dropdown
$products_sql = "SELECT id, name, category FROM products ORDER BY name ASC";
$products_result = mysqli_query($conn, $products_sql);
$products = [];
if ($products_result) {
    while ($row = mysqli_fetch_assoc($products_result)) {
        $products[] = $row;
    }
}

// Get all sales representatives for assignment
$users_sql = "SELECT id, first_name, last_name FROM employees WHERE role IN ('sales_rep', 'manager') ORDER BY first_name, last_name";
$users_result = mysqli_query($conn, $users_sql);
$users = [];
if ($users_result) {
    while ($row = mysqli_fetch_assoc($users_result)) {
        $users[] = $row;
    }
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate first name
    if (empty(trim($_POST["first_name"]))) {
        $first_name_err = "Please enter first name.";
    } else {
        $first_name = sanitize_input(trim($_POST["first_name"]));
    }
    
    // Validate last name
    if (empty(trim($_POST["last_name"]))) {
        $last_name_err = "Please enter last name.";
    } else {
        $last_name = sanitize_input(trim($_POST["last_name"]));
    }
    
    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter email.";
    } else {
        $email = sanitize_input(trim($_POST["email"]));
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $email_err = "Please enter a valid email address.";
        } else {
            // Check if email already exists in leads table (excluding current lead)
            $sql = "SELECT id FROM leads WHERE email = ? AND id != ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "si", $email, $lead_id);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_store_result($stmt);
            
            if (mysqli_stmt_num_rows($stmt) > 0) {
                $email_err = "A lead with this email already exists.";
            }
            mysqli_stmt_close($stmt);
        }
    }
    
    // Validate phone
    if (empty(trim($_POST["phone"]))) {
        $phone_err = "Please enter phone number.";
    } else {
        $phone = sanitize_input(trim($_POST["phone"]));
        if (!preg_match("/^[0-9]{10}$/", $phone)) {
            $phone_err = "Please enter a valid 10-digit phone number.";
        }
    }
    
    // Validate interest
    if (empty(trim($_POST["interest"]))) {
        $interest_err = "Please select a product interest.";
    } else {
        $interest = sanitize_input(trim($_POST["interest"]));
    }
    
    // Get other form data
    $country_code = sanitize_input(trim($_POST["country_code"] ?? '+91'));
    $source = sanitize_input(trim($_POST["source"] ?? ''));
    $notes = sanitize_input(trim($_POST["notes"] ?? ''));
    $status = sanitize_input(trim($_POST["status"] ?? 'new'));
    $assigned_to = !empty($_POST["assigned_to"]) ? intval($_POST["assigned_to"]) : null;
    
    // If no errors, proceed with updating the lead
    if (empty($first_name_err) && empty($last_name_err) && empty($email_err) && empty($phone_err) && empty($interest_err)) {
        // Prepare an update statement for leads table
        $sql = "UPDATE leads SET first_name = ?, last_name = ?, email = ?, phone = ?, country_code = ?, customer_interest = ?, source = ?, notes = ?, status = ?, assigned_to = ?, updated_at = NOW() WHERE id = ?";

        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "sssssssssii", $first_name, $last_name, $email, $phone, $country_code, $interest, $source, $notes, $status, $assigned_to, $lead_id);
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                $success_message = "Lead updated successfully!";
            } else {
                $error_message = "Error: " . mysqli_error($conn);
            }
            
            // Close statement
            mysqli_stmt_close($stmt);
        } else {
            $error_message = "Database error. Please try again later.";
        }
    }
}

$page_title = "Edit Lead";
include __DIR__ . '/includes/header.php';
?>

<script src="https://cdn.tailwindcss.com"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: "#1E3E62",
                    secondary: "#FF6500",
                }
            }
        }
    }
</script>

<?php include __DIR__ . '/includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Edit Lead</h1>
            <div class="flex gap-3 mx-10 mr-16">
                <a href="leads.php" class="inline-flex items-center justify-center bg-white text-[#1E3E62] py-2 px-4 rounded-md hover:bg-gray-100 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Leads
                </a>
                <a href="view_lead.php?id=<?php echo $lead_id; ?>" class="inline-flex items-center justify-center bg-[#FF6500] text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors">
                    <i class="fas fa-eye mr-2"></i> View Lead
                </a>
            </div>
        </div>
    </header>

    <main class="flex-1 overflow-y-auto p-10">
        <!-- Success/Error Messages -->
        <?php if (!empty($success_message)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Edit Lead Form -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                <h3 class="text-lg leading-6 font-medium text-white">
                    Lead Information
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-white">
                    Update the lead's details below
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $lead_id; ?>" class="space-y-6">
                    <!-- Name Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700">First Name <span class="text-red-500">*</span></label>
                            <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($first_name); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border" required>
                            <?php if (!empty($first_name_err)): ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo $first_name_err; ?></p>
                            <?php endif; ?>
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name <span class="text-red-500">*</span></label>
                            <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($last_name); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border" required>
                            <?php if (!empty($last_name_err)): ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo $last_name_err; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email <span class="text-red-500">*</span></label>
                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border" required>
                            <?php if (!empty($email_err)): ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo $email_err; ?></p>
                            <?php endif; ?>
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700">Phone <span class="text-red-500">*</span></label>
                            <div class="flex">
                                <select name="country_code" class="mt-1 focus:ring-blue-500 focus:border-blue-500 shadow-sm sm:text-sm border-gray-300 rounded-l-md p-2 border">
                                    <option value="+91" <?php echo $country_code == '+91' ? 'selected' : ''; ?>>+91</option>
                                    <option value="+1" <?php echo $country_code == '+1' ? 'selected' : ''; ?>>+1</option>
                                    <option value="+44" <?php echo $country_code == '+44' ? 'selected' : ''; ?>>+44</option>
                                </select>
                                <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($phone); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-r-md p-2 border" required>
                            </div>
                            <?php if (!empty($phone_err)): ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo $phone_err; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Product Interest -->
                    <div>
                        <label for="interest" class="block text-sm font-medium text-gray-700">Product Interest <span class="text-red-500">*</span></label>
                        <select id="interest" name="interest" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border" required>
                            <option value="">Select a product</option>
                            <?php if (!empty($products)): ?>
                                <?php foreach ($products as $product): ?>
                                    <option value="<?php echo htmlspecialchars($product['name']); ?>" 
                                            <?php echo $interest == $product['name'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($product['name']); ?>
                                        <?php if (!empty($product['category'])): ?>
                                            (<?php echo htmlspecialchars($product['category']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <option value="General Inquiry" <?php echo $interest == 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                            <?php endif; ?>
                        </select>
                        <?php if (!empty($interest_err)): ?>
                            <p class="text-red-500 text-sm mt-1"><?php echo $interest_err; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Status, Source and Assignment -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select id="status" name="status" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border">
                                <option value="new" <?php echo $status == 'new' ? 'selected' : ''; ?>>New</option>
                                <option value="contacted" <?php echo $status == 'contacted' ? 'selected' : ''; ?>>Contacted</option>
                                <option value="qualified" <?php echo $status == 'qualified' ? 'selected' : ''; ?>>Qualified</option>
                                <option value="lost" <?php echo $status == 'lost' ? 'selected' : ''; ?>>Lost</option>
                            </select>
                        </div>
                        <div>
                            <label for="source" class="block text-sm font-medium text-gray-700">Source</label>
                            <select id="source" name="source" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border">
                                <option value="">Select source</option>
                                <option value="Website" <?php echo $source == 'Website' ? 'selected' : ''; ?>>Website</option>
                                <option value="Referral" <?php echo $source == 'Referral' ? 'selected' : ''; ?>>Referral</option>
                                <option value="Social Media" <?php echo $source == 'Social Media' ? 'selected' : ''; ?>>Social Media</option>
                                <option value="Email Campaign" <?php echo $source == 'Email Campaign' ? 'selected' : ''; ?>>Email Campaign</option>
                                <option value="Cold Call" <?php echo $source == 'Cold Call' ? 'selected' : ''; ?>>Cold Call</option>
                                <option value="Other" <?php echo $source == 'Other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>
                        <div>
                            <label for="assigned_to" class="block text-sm font-medium text-gray-700">Assign To</label>
                            <select id="assigned_to" name="assigned_to" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border">
                                <option value="">Unassigned</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $assigned_to == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border"><?php echo htmlspecialchars($notes); ?></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="submit" class="bg-[#1E3E62] text-white font-medium py-3 px-6 rounded-md hover:bg-[#0B192C] transition-colors">
                            Update Lead
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
