<?php
// This file contains the common navigation sidebar for all dashboard pages

// Determine if the current script is in a subfolder (like /auth/ or /includes/)
$script_path = $_SERVER['SCRIPT_NAME'];
$is_in_subfolder = strpos($script_path, '/auth/') !== false || strpos($script_path, '/includes/') !== false;

// Helper for path prefix
$prefix = $is_in_subfolder ? '../' : '';

// Get unread notification count
$unread_count = 0;
if (isset($_SESSION['user_id'])) {
    $notification_sql = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0";
    $notification_stmt = mysqli_prepare($conn, $notification_sql);
    mysqli_stmt_bind_param($notification_stmt, "i", $_SESSION['user_id']);
    mysqli_stmt_execute($notification_stmt);
    $notification_result = mysqli_stmt_get_result($notification_stmt);
    $unread_count = mysqli_fetch_assoc($notification_result)['count'];
}
?>
<!-- Top Right Icons -->
<div class="fixed top-4 right-4 z-50 flex items-center gap-2">
    <!-- Notification Icon -->
    <div class="relative">
        <button id="notification-btn" class="bg-[#0b192c] text-white p-2 rounded shadow-lg hover:bg-[#1e3e62] transition-colors">
            <i class="fas fa-bell text-lg"></i>
            <?php if ($unread_count > 0): ?>
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                <?php echo $unread_count > 9 ? '9+' : $unread_count; ?>
            </span>
            <?php endif; ?>
        </button>

        <!-- Notification Dropdown -->
        <div id="notification-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border z-30">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
            </div>
            <div class="max-h-64 overflow-y-auto">
                <?php
                // Get recent notifications
                try {
                    $notifications_sql = "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
                    $notifications_stmt = mysqli_prepare($conn, $notifications_sql);
                    mysqli_stmt_bind_param($notifications_stmt, "i", $_SESSION['user_id']);
                    mysqli_stmt_execute($notifications_stmt);
                    $notifications_result = mysqli_stmt_get_result($notifications_stmt);

                    if (mysqli_num_rows($notifications_result) > 0):
                        while ($notification = mysqli_fetch_assoc($notifications_result)):
                ?>
                <div class="p-3 border-b border-gray-100 hover:bg-gray-50 <?php echo $notification['is_read'] ? '' : 'bg-blue-50'; ?>">
                    <p class="text-sm text-gray-800"><?php echo htmlspecialchars($notification['message']); ?></p>
                    <p class="text-xs text-gray-500 mt-1"><?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?></p>
                </div>
                <?php
                        endwhile;
                    else:
                ?>
                <div class="p-4 text-center text-gray-500">
                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                    <p>No notifications</p>
                </div>
                <?php
                    endif;
                } catch (Exception $e) {
                    // If notifications table doesn't exist, show no notifications
                ?>
                <div class="p-4 text-center text-gray-500">
                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                    <p>No notifications</p>
                </div>
                <?php } ?>
            </div>
            <div class="p-3 border-t border-gray-200 text-center">
                <a href="<?php echo $prefix; ?>notifications.php" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All Notifications</a>
            </div>
        </div>
    </div>

    <!-- Mobile/Tablet Menu Toggle Button -->
    <button id="menu-toggle" class="bg-[#0b192c] text-white px-3 py-1 shadow-lg hover:bg-[#1e3e62] transition-colors" onclick="document.getElementById('sidebar').classList.remove('-translate-x-full'); this.classList.add('hidden');">
      <i class="fas fa-bars text-xl"></i>
    </button>
</div>

<style>
@media (min-width: 870px) {
  #menu-toggle {
    display: none !important;
  }
}
#menu-toggle.hidden {
  display: none !important;
}
</style>

<!-- Sidebar -->
<?php if (has_role("sales_rep")) : ?>
<!-- Sales Representative Sidebar -->
<div id="sidebar" class="fixed left-0 top-0 w-56 min-[870px]:w-60 h-screen bg-[#0b192C] flex flex-col justify-between items-center py-2 min-[870px]:py-4 z-20 transform -translate-x-full min-[870px]:translate-x-0 transition-transform duration-300 ease-in-out">
    <!-- Close button for mobile/tablet -->
    <button class="close-menu-btn absolute top-2 right-2 text-white min-[870px]:hidden hover:text-orange-500 transition-colors" onclick="document.getElementById('sidebar').classList.add('-translate-x-full'); document.getElementById('menu-toggle').classList.remove('hidden');">
      <i class="fas fa-times text-xl"></i>
    </button>
    
    <div class="mb-6 md:mb-8 lg:mb-10 mt-8 md:mt-0">
      <img src="<?php echo $prefix; ?>img/logo.png" alt="Logo" class="w-32 md:w-36 lg:w-40" />
    </div>
    <nav class="flex flex-col gap-4 md:gap-5 lg:gap-6">
      <a href="<?php echo $prefix; ?>sales_rep_dashboard.php" class="flex items-center gap-2 <?php echo is_active_page('sales_rep_dashboard.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/dashboard.png" alt="Dashboard" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Dashboard</span>
      </a>
      <a href="<?php echo $prefix; ?>leads.php" class="flex items-center gap-2 <?php echo is_active_page('leads.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/leads.png" alt="My Leads" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">My Leads</span>
      </a>
      <a href="<?php echo $prefix; ?>my_contacts.php" class="flex items-center gap-2 <?php echo is_active_page('my_contacts.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-address-book w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">My Contacts</span>
      </a>
      <a href="<?php echo $prefix; ?>my_sales.php" class="flex items-center gap-2 <?php echo is_active_page('my_sales.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="My Sales" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">My Sales</span>
      </a>
      <a href="<?php echo $prefix; ?>create_sale.php" class="flex items-center gap-2 <?php echo is_active_page('create_sale.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="Create Sale" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Create Sale</span>
      </a>
      <a href="<?php echo $prefix; ?>products.php" class="flex items-center gap-2 <?php echo is_active_page('products.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/products.png" alt="Products" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Products</span>
      </a>
      <?php if (has_role("admin")) : ?>
      <a href="<?php echo $prefix; ?>employees.php" class="flex items-center gap-2 <?php echo is_active_page('employees.php') || is_active_page('edit_employee.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-users w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Employees</span>
      </a>
      <?php endif; ?>
    </nav>
    <div class="flex flex-col items-center mb-2">
      <!-- Notification Bell -->
      <div class="relative mb-3">
        <button id="notification-bell" class="text-white hover:text-orange-500 transition-colors">
          <i class="fas fa-bell text-xl"></i>
          <span id="notification-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center <?php echo $unread_count > 0 ? '' : 'hidden'; ?>">
            <?php echo $unread_count; ?>
          </span>
        </button>
        
        <!-- Notification Dropdown -->
        <div id="notification-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50 overflow-hidden">
          <div class="px-4 py-2 bg-gray-100 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-medium text-gray-700">Notifications</h3>
            <button class="text-gray-500 hover:text-gray-700" onclick="markAllAsRead()">
              <i class="fas fa-check-double"></i> Mark all as read
            </button>
          </div>
          <ul id="notification-list" class="max-h-96 overflow-y-auto">
            <!-- Notifications will be loaded here via JavaScript -->
            <li class="py-2 px-4 text-gray-500 text-center">Loading notifications...</li>
          </ul>
          <div class="px-4 py-2 bg-gray-100 border-t border-gray-200 text-center">
            <a href="<?php echo $prefix; ?>notifications.php" class="text-blue-600 hover:text-blue-800 text-sm">
              View all notifications
            </a>
          </div>
        </div>
      </div>
      
      <div class="flex items-center gap-2 bg-[#0a1525] rounded-lg p-2">
        <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center">
          <img src="<?php echo $prefix; ?>img/user.png" alt="User" class="w-5 h-5" />
        </div>
        <div class="flex flex-col">
          <p class="font-semibold text-white text-xs leading-tight truncate max-w-[100px]">
            <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?>
          </p>
          <p class="text-[10px] text-gray-300 leading-tight"><?php echo htmlspecialchars(ucfirst($_SESSION["role"])); ?></p>
        </div>
      </div>
      <a href="<?php echo $prefix; ?>auth/logout.php" id="logout-btn" class="mt-2 bg-[#FF6500] text-white px-2 py-1 rounded-md flex items-center gap-1 hover:bg-orange-600 text-xs">
        <i class="fas fa-sign-out-alt text-[10px]"></i>
        <span class="font-medium text-[10px]">LOG OUT</span>
      </a>
    </div>
</div>
<?php else : ?>
<!-- Admin Sidebar -->
<div id="sidebar" class="fixed left-0 top-0 w-56 min-[870px]:w-60 h-screen bg-[#0b192C] flex flex-col justify-between items-center py-2 min-[870px]:py-4 z-20 transform -translate-x-full min-[870px]:translate-x-0 transition-transform duration-300 ease-in-out">
    <button class="close-menu-btn absolute top-2 right-2 text-white min-[870px]:hidden p-2 hover:text-orange-500 transition-colors" onclick="document.getElementById('sidebar').classList.add('-translate-x-full'); document.getElementById('menu-toggle').classList.remove('hidden');">
      <i class="fas fa-times text-xl"></i>
    </button>
    <div class="mb-6 md:mb-8 lg:mb-10 mt-8 md:mt-0">
      <img src="<?php echo $prefix; ?>img/logo.png" alt="Logo" class="w-32 md:w-36 lg:w-40" />
    </div>
    <nav class="flex flex-col gap-4 md:gap-5 lg:gap-6">
      <a href="<?php echo $prefix; ?>admin.php" class="flex items-center gap-2 <?php echo is_active_page('admin.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/dashboard.png" alt="Admin" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Admin</span>
      </a>
      <a href="<?php echo $prefix; ?>leads.php" class="flex items-center gap-2 <?php echo is_active_page('leads.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/leads.png" alt="Leads" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Leads</span>
      </a>
      <a href="<?php echo $prefix; ?>contacts.php" class="flex items-center gap-2 <?php echo is_active_page('contacts.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-address-book w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Contacts</span>
      </a>
      <a href="<?php echo $prefix; ?>sales.php" class="flex items-center gap-2 <?php echo is_active_page('sales.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="Sales" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Sales</span>
      </a>
      <a href="<?php echo $prefix; ?>products.php" class="flex items-center gap-2 <?php echo is_active_page('products.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/products.png" alt="Products" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Products</span>
      </a>
      <?php if (has_role("admin")) : ?>
      <a href="<?php echo $prefix; ?>employees.php" class="flex items-center gap-2 <?php echo is_active_page('employees.php') || is_active_page('edit_employee.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-users w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Employees</span>
      </a>
      <?php endif; ?>
    </nav>
    <div class="flex flex-col items-center mb-2">
      <!-- Notification Bell -->
      <div class="relative mb-3">
        <button id="notification-bell" class="text-white hover:text-orange-500 transition-colors">
          <i class="fas fa-bell text-xl"></i>
          <span id="notification-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center <?php echo $unread_count > 0 ? '' : 'hidden'; ?>">
            <?php echo $unread_count; ?>
          </span>
        </button>
        
        <!-- Notification Dropdown -->
        <div id="notification-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50 overflow-hidden">
          <div class="px-4 py-2 bg-gray-100 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-medium text-gray-700">Notifications</h3>
            <button class="text-gray-500 hover:text-gray-700" onclick="markAllAsRead()">
              <i class="fas fa-check-double"></i> Mark all as read
            </button>
          </div>
          <ul id="notification-list" class="max-h-96 overflow-y-auto">
            <!-- Notifications will be loaded here via JavaScript -->
            <li class="py-2 px-4 text-gray-500 text-center">Loading notifications...</li>
          </ul>
          <div class="px-4 py-2 bg-gray-100 border-t border-gray-200 text-center">
            <a href="<?php echo $prefix; ?>notifications.php" class="text-blue-600 hover:text-blue-800 text-sm">
              View all notifications
            </a>
          </div>
        </div>
      </div>
      
      <div class="flex items-center gap-2 bg-[#0a1525] rounded-lg p-2">
        <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center">
          <img src="<?php echo $prefix; ?>img/user.png" alt="User" class="w-5 h-5" />
        </div>
        <div class="flex flex-col">
          <p class="font-semibold text-white text-xs leading-tight truncate max-w-[100px]">
            <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?>
          </p>
          <p class="text-[10px] text-gray-300 leading-tight"><?php echo htmlspecialchars(ucfirst($_SESSION["role"])); ?></p>
        </div>
      </div>
      <a href="<?php echo $prefix; ?>auth/logout.php" id="logout-btn" class="mt-2 bg-[#FF6500] text-white px-2 py-1 rounded-md flex items-center gap-1 hover:bg-orange-600 text-xs">
        <i class="fas fa-sign-out-alt text-[10px]"></i>
        <span class="font-medium text-[10px]">LOG OUT</span>
      </a>
    </div>
</div>
<?php endif; ?>

<!-- Include notifications script -->
<script src="<?php echo $prefix; ?>js/notifications.js"></script>

<script>
// Top notification dropdown functionality
document.addEventListener('DOMContentLoaded', function() {
    const notificationBtn = document.getElementById('notification-btn');
    const notificationDropdown = document.getElementById('notification-dropdown');

    if (notificationBtn && notificationDropdown) {
        // Toggle notification dropdown
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationDropdown.classList.toggle('hidden');
        });

        // Close notification dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!notificationDropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });
    }
});
</script>
