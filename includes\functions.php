<?php
// Include validation functions
require_once __DIR__ . '/validation.php';
// Include database functions
require_once __DIR__ . '/db_functions.php';
// Include security functions
require_once __DIR__ . '/security.php';

// Start session if not already started
function ensure_session_started() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
}

// Check if user is logged in
function is_logged_in() {
    ensure_session_started();
    return isset($_SESSION['user_id']);
}

// Check if user has specific role
function has_role($role) {
    ensure_session_started();
    return isset($_SESSION['role']) && $_SESSION['role'] == $role;
}

// Check if current user has a specific role or any role from an array of roles
function current_user_has_role($roles) {
    ensure_session_started();
    if (!isset($_SESSION['role'])) {
        return false;
    }
    
    // If $roles is an array, check if user has any of those roles
    if (is_array($roles)) {
        return in_array($_SESSION['role'], $roles);
    }
    
    // If $roles is a string, check if user has that specific role
    return $_SESSION['role'] == $roles;
}

// Determine if the current page matches the given page name or is related to it
function is_active_page($page_name) {
    // Get the current script filename without the path
    $current_page = basename($_SERVER['SCRIPT_NAME']);
    
    // Direct match
    if ($current_page === $page_name) {
        return true;
    }
    
    // Check for related pages
    $related_pages = [
        'leads.php' => [
            'edit_lead.php', 
            'view_lead.php', 
            'my_leads.php', 
            'convert_lead.php', 
            'convert_lead_form.php',
            'import_leads.php',
            'delete_lead.php',
            'update_lead_status.php'
        ],
        'sales.php' => [
            'edit_sale.php', 
            'view_sale.php', 
            'my_sales.php', 
            'create_sale.php', 
            'process_sale.php',
            'delete_sale.php',
            'get_sales_report.php',
            'sales_rep_dashboard.php',
            'check_sales_table.php',
            'update_sales_table.php',
            'invoice.php'  // Added invoice page
        ],
        'my_sales.php' => [
            'edit_sale.php',
            'view_sale.php',
            'process_sale.php',
            'delete_sale.php',
            'invoice.php'  // Added invoice page for sales rep
        ],
        'create_sale.php' => [
            'process_sale.php'
        ],

        'products.php' => [
            'edit_product.php',
            'delete_product.php'
        ],
        'employees.php' => [
            'edit_employee.php',
            'view_employee.php'
        ],
        'contacts.php' => [
            'view_contact.php',
            'edit_contact.php',
            'create_contact.php'
        ],
        'referrals.php' => [
            'edit_referral.php',
            'delete_referral.php'
        ]
    ];
    
    // Check if current page is related to the given page
    if (isset($related_pages[$page_name]) && in_array($current_page, $related_pages[$page_name])) {
        return true;
    }
    
    return false;
}

// Check if user has any of the specified roles
function has_any_role($roles) {
    ensure_session_started();
    if (!isset($_SESSION['role'])) {
        return false;
    }
    return in_array($_SESSION['role'], $roles);
}

// Redirect to login page if not logged in
function require_login() {
    if (!is_logged_in()) {
        header("Location: auth/login.php");
        exit;
    }
}

// Redirect to login page if not authorized for role
function require_role($role) {
    require_login();
    if (!has_role($role)) {
        header("Location: auth/unauthorized.php");
        exit;
    }
}

// Redirect to login page if not authorized for any of the roles
function require_any_role($roles) {
    require_login();
    if (!has_any_role($roles)) {
        header("Location: auth/unauthorized.php");
        exit;
    }
}

/**
 * Sanitize input data with enhanced security
 * 
 * @param mixed $data Data to sanitize
 * @param mysqli $conn Optional database connection for SQL escaping
 * @return mixed Sanitized data
 */
function sanitize_input($data, $conn = null) {
    // Handle arrays recursively
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitize_input($value, $conn);
        }
        return $data;
    }
    
    // Handle strings
    if (is_string($data)) {
        // Basic sanitization
        $data = trim($data);
        $data = stripslashes($data);
        
        // Remove null bytes (potential null byte injection)
        $data = str_replace("\0", '', $data);
        
        // HTML encode special characters to prevent XSS
        $data = htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        // SQL escape if connection provided
        if ($conn !== null) {
            $data = mysqli_real_escape_string($conn, $data);
        }
    }
    
    return $data;
}

// Sanitize and validate input based on type
function sanitize_validate_input($data, $type = 'text') {
    $result = sanitize_and_validate($data, $type);
    return $result['value']; // Return sanitized value
}

// Format currency
function format_currency($amount) {
    // Handle null or empty values
    if ($amount === null || $amount === '' || !is_numeric($amount)) {
        return '₹0.00';
    }

    // Convert to float to ensure proper numeric handling
    $amount = (float) $amount;

    // For large numbers, display the raw value without formatting
    if ($amount > 999999) {
        return '₹' . number_format($amount, 0);
    }

    // For smaller numbers, use the standard formatting
    if (floor($amount) == $amount) {
        return '₹' . number_format($amount, 0);
    } else {
        return '₹' . number_format($amount, 2);
    }
}

// Calculate conversion rate
function calculate_conversion_rate($total_leads, $total_sales) {
    if ($total_leads == 0) {
        return '0%';
    }
    $rate = ($total_sales / $total_leads) * 100;
    return round($rate, 1) . '%';
}

/**
 * Get user name by ID using secure database functions
 *
 * @param mysqli $conn Database connection
 * @param int $user_id User ID
 * @return string User's full name or 'Unknown' if not found
 */
function get_user_name($conn, $user_id) {
    $sql = "SELECT CONCAT(first_name, ' ', last_name) AS name FROM employees WHERE id = ?";
    $result = db_select_single($conn, $sql, "i", [$user_id]);

    if ($result && isset($result['name'])) {
        return $result['name'];
    }

    return 'Unknown';
}

/**
 * Convert lead to customer/contact
 *
 * @param mysqli $conn Database connection
 * @param int $lead_id Lead ID to convert
 * @return array Result array with success status and message
 */
function convert_lead_to_customer($conn, $lead_id) {
    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // Get lead data
        $lead_sql = "SELECT * FROM leads WHERE id = ? AND status != 'converted'";
        $lead_stmt = mysqli_prepare($conn, $lead_sql);
        mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
        mysqli_stmt_execute($lead_stmt);
        $lead_result = mysqli_stmt_get_result($lead_stmt);
        $lead = mysqli_fetch_assoc($lead_result);

        if (!$lead) {
            throw new Exception("Lead not found or already converted");
        }

        // Check if contact with same email already exists
        $existing_contact_sql = "SELECT id FROM contacts WHERE email = ?";
        $existing_stmt = mysqli_prepare($conn, $existing_contact_sql);
        mysqli_stmt_bind_param($existing_stmt, "s", $lead['email']);
        mysqli_stmt_execute($existing_stmt);
        $existing_result = mysqli_stmt_get_result($existing_stmt);

        if (mysqli_num_rows($existing_result) > 0) {
            throw new Exception("A contact with this email already exists");
        }

        // Insert into contacts table
        $contact_sql = "INSERT INTO contacts (
            first_name, last_name, email, phone, country_code,
            customer_interest, source, assigned_to, created_by,
            created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

        $contact_stmt = mysqli_prepare($conn, $contact_sql);
        mysqli_stmt_bind_param($contact_stmt, "sssssssii",
            $lead['first_name'],
            $lead['last_name'],
            $lead['email'],
            $lead['phone'],
            $lead['country_code'],
            $lead['customer_interest'],
            $lead['source'],
            $lead['assigned_to'],
            $lead['created_by']
        );

        if (!mysqli_stmt_execute($contact_stmt)) {
            throw new Exception("Failed to create contact: " . mysqli_error($conn));
        }

        $contact_id = mysqli_insert_id($conn);

        // Update lead status to converted and set reference to contact
        $update_lead_sql = "UPDATE leads SET
            status = 'converted',
            converted_to_contact_id = ?,
            updated_at = NOW()
            WHERE id = ?";
        $update_stmt = mysqli_prepare($conn, $update_lead_sql);
        mysqli_stmt_bind_param($update_stmt, "ii", $contact_id, $lead_id);

        if (!mysqli_stmt_execute($update_stmt)) {
            throw new Exception("Failed to update lead status: " . mysqli_error($conn));
        }

        // Delete the lead from leads table (as per requirement)
        $delete_lead_sql = "DELETE FROM leads WHERE id = ?";
        $delete_stmt = mysqli_prepare($conn, $delete_lead_sql);
        mysqli_stmt_bind_param($delete_stmt, "i", $lead_id);

        if (!mysqli_stmt_execute($delete_stmt)) {
            throw new Exception("Failed to remove lead: " . mysqli_error($conn));
        }

        // Create notification for successful conversion
        $notification_message = "Lead converted to customer: {$lead['first_name']} {$lead['last_name']} has been successfully converted to a customer.";
        $notification_link = "view_contact.php?id=" . $contact_id;

        // Get all admins and managers for notification
        $users_sql = "SELECT id FROM employees WHERE role IN ('admin', 'manager')";
        $users_result = mysqli_query($conn, $users_sql);

        while ($user = mysqli_fetch_assoc($users_result)) {
            try {
                // Check what columns exist in notifications table
                $check_columns_sql = "SHOW COLUMNS FROM notifications";
                $check_columns_result = mysqli_query($conn, $check_columns_sql);
                $available_columns = [];

                while ($column = mysqli_fetch_assoc($check_columns_result)) {
                    $available_columns[] = $column['Field'];
                }

                // Build the insert query based on available columns
                if (in_array('link', $available_columns) && in_array('type', $available_columns)) {
                    $notification_sql = "INSERT INTO notifications (user_id, message, link, type, created_at) VALUES (?, ?, ?, 'lead_converted', NOW())";
                    $notification_stmt = mysqli_prepare($conn, $notification_sql);
                    mysqli_stmt_bind_param($notification_stmt, "iss", $user['id'], $notification_message, $notification_link);
                } elseif (in_array('link', $available_columns)) {
                    $notification_sql = "INSERT INTO notifications (user_id, message, link, created_at) VALUES (?, ?, ?, NOW())";
                    $notification_stmt = mysqli_prepare($conn, $notification_sql);
                    mysqli_stmt_bind_param($notification_stmt, "iss", $user['id'], $notification_message, $notification_link);
                } else {
                    $notification_sql = "INSERT INTO notifications (user_id, message, created_at) VALUES (?, ?, NOW())";
                    $notification_stmt = mysqli_prepare($conn, $notification_sql);
                    mysqli_stmt_bind_param($notification_stmt, "is", $user['id'], $notification_message);
                }

                mysqli_stmt_execute($notification_stmt);
            } catch (Exception $e) {
                // Continue without failing the conversion if notifications fail
                error_log("Notification creation failed during lead conversion: " . $e->getMessage());
            }
        }

        // Commit transaction
        mysqli_commit($conn);

        return [
            'success' => true,
            'message' => 'Lead successfully converted to customer',
            'customer_id' => $contact_id
        ];

    } catch (Exception $e) {
        // Rollback transaction
        mysqli_rollback($conn);

        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}



/**
 * Get product name by ID using secure database functions
 * 
 * @param mysqli $conn Database connection
 * @param int $product_id Product ID
 * @return string Product name or 'Unknown' if not found
 */
function get_product_name($conn, $product_id) {
    $sql = "SELECT name FROM products WHERE id = ?";
    $result = db_select_single($conn, $sql, "i", [$product_id]);
    
    if ($result && isset($result['name'])) {
        return $result['name'];
    }
    
    return 'Unknown';
}

// Generate a unique invoice number
function generate_invoice_number() {
    return 'INV-' . date('Ymd') . '-' . rand(1000, 9999);
}



/**
 * Generate a secure remember me token
 * 
 * @return string A 64-character hexadecimal token
 */
function generate_remember_token() {
    return bin2hex(random_bytes(32)); // 32 bytes = 64 hex characters
}

/**
 * Set remember me cookies and update database with token and expiry
 * 
 * @param int $user_id The user ID
 * @param string $token The remember token
 * @param int $expiry_days Number of days until token expires (default: 30)
 * @param object $conn Database connection object (optional)
 * @return bool True on success, false on failure
 */
function set_remember_cookie($user_id, $token, $expiry_days = 30, $conn = null) {
    // Calculate expiry timestamp for cookies
    $cookie_expiry = time() + (86400 * $expiry_days); // 86400 = 1 day in seconds
    
    // Cookie parameters
    $path = '/';
    $domain = '';
    $secure = false; // Set to true if using HTTPS
    $httponly = true;
    
    // Set cookies with proper parameters for better security and compatibility
    // Using the traditional format for better compatibility with older PHP versions
    $cookie1 = setcookie('remember_user', $user_id, $cookie_expiry, $path, $domain, $secure, $httponly);
    $cookie2 = setcookie('remember_token', $token, $cookie_expiry, $path, $domain, $secure, $httponly);
    
    // If a database connection was provided, update the token and token_expiry in the database
    if ($conn !== null) {
        // Calculate expiry date for database (in MySQL datetime format)
        $db_expiry = date('Y-m-d H:i:s', $cookie_expiry);
        
        // Update both the remember_token and token_expiry in the database
        $sql = "UPDATE employees SET remember_token = ?, token_expiry = ? WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssi", $token, $db_expiry, $user_id);
        $db_update = mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        return ($cookie1 && $cookie2 && $db_update);
    }
    
    return ($cookie1 && $cookie2);
}

/**
 * Clear remember me cookies and token in database
 * 
 * @param int $user_id The user ID (optional)
 * @param object $conn Database connection object (optional)
 * @return bool True on success
 */
function clear_remember_cookie($user_id = null, $conn = null) {
    $expiry = time() - 3600; // Set expiration to the past
    $path = '/';
    $domain = '';
    $secure = false;
    $httponly = true;
    
    // Clear cookies using traditional format for better compatibility
    setcookie('remember_user', '', $expiry, $path, $domain, $secure, $httponly);
    setcookie('remember_token', '', $expiry, $path, $domain, $secure, $httponly);
    
    // If user_id and connection are provided, clear the token in the database
    if ($user_id !== null && $conn !== null) {
        $sql = "UPDATE employees SET remember_token = NULL, token_expiry = NULL WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    } else if ($conn !== null && isset($_COOKIE['remember_user'])) {
        // If we have a connection but no user_id, try to get it from the cookie
        $cookie_user_id = $_COOKIE['remember_user'];
        $sql = "UPDATE employees SET remember_token = NULL, token_expiry = NULL WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $cookie_user_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }
    
    return true;
}

/**
 * Check if a remember token is valid and not expired
 * 
 * @param int $user_id The user ID
 * @param string $token The remember token
 * @param object $conn Database connection object
 * @return bool True if token is valid and not expired, false otherwise
 */
function is_valid_remember_token($user_id, $token, $conn) {
    $sql = "SELECT remember_token, token_expiry FROM employees WHERE id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($row = mysqli_fetch_assoc($result)) {
        // Check if token exists
        if (empty($row['remember_token']) || empty($row['token_expiry'])) {
            return false;
        }
        
        // Check if token matches
        $token_matches = hash_equals($row['remember_token'], $token);
        
        // Check if token is not expired
        $current_time = time();
        $expiry_time = strtotime($row['token_expiry']);
        $not_expired = ($expiry_time > $current_time);
        
        // Return true only if both conditions are met
        if ($token_matches && $not_expired) {
            return true;
        }
    }
    
    return false;
}

/**
 * Refresh the remember token and extend its expiry
 * 
 * @param int $user_id The user ID
 * @param object $conn Database connection object
 * @param int $expiry_days Number of days until token expires (default: 30)
 * @return string|bool The new token on success, false on failure
 */
function refresh_remember_token($user_id, $conn, $expiry_days = 30) {
    // Generate a new token
    $new_token = generate_remember_token();
    
    // Use the set_remember_cookie function to handle both cookie setting and database update
    if (set_remember_cookie($user_id, $new_token, $expiry_days, $conn)) {
        return $new_token;
    }
    
    return false;
}
