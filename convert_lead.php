<?php
// Include database connection and functions
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!is_logged_in()) {
    header("Location: login.php");
    exit();
}

$success_message = '';
$error_message = '';

// Get lead ID from URL
$lead_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($lead_id <= 0) {
    header("Location: leads.php");
    exit();
}

// Get lead details
$lead_sql = "SELECT * FROM leads WHERE id = ? AND status != 'converted'";
$lead_stmt = mysqli_prepare($conn, $lead_sql);
mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
mysqli_stmt_execute($lead_stmt);
$lead_result = mysqli_stmt_get_result($lead_stmt);
$lead = mysqli_fetch_assoc($lead_result);

if (!$lead) {
    $_SESSION['error_message'] = "Lead not found or already converted.";
    header("Location: leads.php");
    exit();
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['confirm_convert'])) {
        $conversion_result = convert_lead_to_customer($conn, $lead_id);
        
        if ($conversion_result['success']) {
            $_SESSION['success_message'] = $conversion_result['message'];
            header("Location: contacts.php");
            exit();
        } else {
            $error_message = $conversion_result['message'];
        }
    }
}

$page_title = "Convert Lead to Customer";
include __DIR__ . '/includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Convert Lead to Customer</h1>
                    <p class="text-gray-600 mt-1">Review lead information before converting to customer</p>
                </div>
                <a href="leads.php" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Leads
                </a>
            </div>
        </div>

        <!-- Error Message -->
        <?php if (!empty($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span><?php echo htmlspecialchars($error_message); ?></span>
            </div>
        </div>
        <?php endif; ?>

        <!-- Lead Information -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Lead Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo htmlspecialchars($lead['first_name']); ?>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo htmlspecialchars($lead['last_name']); ?>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo htmlspecialchars($lead['email']); ?>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo htmlspecialchars($lead['country_code'] . ' ' . $lead['phone']); ?>
                    </div>
                </div>
                
                <?php if (!empty($lead['company'])): ?>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo htmlspecialchars($lead['company']); ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Source</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo htmlspecialchars($lead['source']); ?>
                    </div>
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Customer Interest</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo htmlspecialchars($lead['customer_interest']); ?>
                    </div>
                </div>
                
                <?php if (!empty($lead['notes'])): ?>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <div class="bg-gray-50 px-3 py-2 rounded-md border">
                        <?php echo nl2br(htmlspecialchars($lead['notes'])); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Conversion Warning -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">Important Notice</h3>
                    <ul class="text-yellow-700 space-y-1">
                        <li>• This lead will be converted to a customer/contact</li>
                        <li>• The lead will be moved to the contacts section</li>
                        <li>• The lead will be automatically removed from the leads table</li>
                        <li>• This action cannot be undone</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Conversion Form -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <form method="POST" action="">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Confirm Conversion</h3>
                        <p class="text-gray-600 mt-1">Are you sure you want to convert this lead to a customer?</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="leads.php" class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 transition-colors">
                            Cancel
                        </a>
                        <button type="submit" name="confirm_convert" 
                                class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors">
                            <i class="fas fa-user-check mr-2"></i>Convert to Customer
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
