<?php
// Include database connection
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/phpmailer_config.php';

// Initialize variables for form
$first_name = $last_name = $email = $phone = $interest = $source = '';
$first_name_err = $last_name_err = $email_err = $phone_err = $interest_err = '';
$success_message = $error_message = '';

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate first name
    if (empty(trim($_POST["first_name"]))) {
        $first_name_err = "Please enter your first name.";
    } else {
        $first_name = sanitize_input(trim($_POST["first_name"]));
    }
    
    // Validate last name
    if (empty(trim($_POST["last_name"]))) {
        $last_name_err = "Please enter your last name.";
    } else {
        $last_name = sanitize_input(trim($_POST["last_name"]));
    }
    
    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter your email.";
    } else {
        $email = sanitize_input(trim($_POST["email"]));
        // Check if email is valid
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $email_err = "Please enter a valid email address.";
        } else {
            // Check if email already exists in leads table
            $sql = "SELECT id FROM leads WHERE email = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "s", $email);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_store_result($stmt);
            
            if (mysqli_stmt_num_rows($stmt) > 0) {
                $email_err = "This email has already submitted an inquiry.";
            }
            mysqli_stmt_close($stmt);
        }
    }
    
    // Validate phone
    if (empty(trim($_POST["phone"]))) {
        $phone_err = "Please enter your phone number.";
    } else {
        $phone = sanitize_input(trim($_POST["phone"]));
        // Simple phone validation (can be enhanced)
        if (!preg_match("/^[0-9]{10}$/", $phone)) {
            $phone_err = "Please enter a valid 10-digit phone number.";
        }
    }
    
    // Validate interest
    if (empty(trim($_POST["interest"]))) {
        $interest_err = "Please specify your interest.";
    } else {
        $interest = sanitize_input(trim($_POST["interest"]));
    }
    
    // Get optional fields
    $source = !empty($_POST["source"]) ? sanitize_input(trim($_POST["source"])) : 'Web Form';

    // If no errors, proceed with saving the lead
    if (empty($first_name_err) && empty($last_name_err) && empty($email_err) && empty($phone_err) && empty($interest_err)) {
        // Prepare an insert statement for leads table
        $sql = "INSERT INTO leads (first_name, last_name, email, phone, customer_interest, source, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, 'new', NOW())";

        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "ssssss", $first_name, $last_name, $email, $phone, $interest, $source);
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // Get the ID of the new lead
                $lead_id = mysqli_insert_id($conn);
                
                // Create notification for all admins and managers
                $notification_message = "New Lead Submitted: $first_name $last_name has submitted a new lead inquiry.";
                $notification_link = "view_lead.php?id=" . $lead_id;
                
                // Get all admins and managers
                $users_sql = "SELECT id FROM employees WHERE role IN ('admin', 'manager')";
                $users_result = mysqli_query($conn, $users_sql);
                
                while ($user = mysqli_fetch_assoc($users_result)) {
                    try {
                        // Check what columns exist in notifications table
                        $check_columns_sql = "SHOW COLUMNS FROM notifications";
                        $check_columns_result = mysqli_query($conn, $check_columns_sql);
                        $available_columns = [];

                        while ($column = mysqli_fetch_assoc($check_columns_result)) {
                            $available_columns[] = $column['Field'];
                        }

                        // Build the insert query based on available columns
                        if (in_array('link', $available_columns) && in_array('type', $available_columns)) {
                            // Full featured notifications table
                            $notify_sql = "INSERT INTO notifications (user_id, message, link, type, created_at)
                                          VALUES (?, ?, ?, 'lead_created', NOW())";
                            $notify_stmt = mysqli_prepare($conn, $notify_sql);
                            mysqli_stmt_bind_param($notify_stmt, "iss", $user['id'], $notification_message, $notification_link);
                        } elseif (in_array('link', $available_columns)) {
                            // Has link but no type column
                            $notify_sql = "INSERT INTO notifications (user_id, message, link, created_at)
                                          VALUES (?, ?, ?, NOW())";
                            $notify_stmt = mysqli_prepare($conn, $notify_sql);
                            mysqli_stmt_bind_param($notify_stmt, "iss", $user['id'], $notification_message, $notification_link);
                        } else {
                            // Basic notifications table - just user_id and message
                            $notify_sql = "INSERT INTO notifications (user_id, message, created_at)
                                          VALUES (?, ?, NOW())";
                            $notify_stmt = mysqli_prepare($conn, $notify_sql);
                            mysqli_stmt_bind_param($notify_stmt, "is", $user['id'], $notification_message);
                        }

                        mysqli_stmt_execute($notify_stmt);
                        mysqli_stmt_close($notify_stmt);
                    } catch (Exception $e) {
                        // If there's any error with notifications, continue without failing the lead creation
                        error_log("Notification creation failed: " . $e->getMessage());
                    }
                }
                
                // Send email notification to the lead
                // Get the lead_created email template
                $subject = "Thank You for Your Interest";
                $body = "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;'>
                    <h2 style='color: #1E3E62;'>Thank You for Contacting Us</h2>
                    <p>Dear {$first_name} {$last_name},</p>
                    <p>Thank you for your interest in our services. We have received your inquiry and one of our representatives will contact you shortly.</p>
                    <p>Here's a summary of the information you provided:</p>
                    <ul>
                        <li><strong>Name:</strong> {$first_name} {$last_name}</li>
                        <li><strong>Email:</strong> {$email}</li>
                        <li><strong>Phone:</strong> {$phone}</li>
                        <li><strong>Interest:</strong> {$interest}</li>
                    </ul>
                    <p>If you have any immediate questions, please don't hesitate to reply to this email.</p>
                    <p>Best regards,<br>The Customer Support Team</p>
                </div>";
                
                try {
                    // Try to get template from database if the table exists
                    $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_created' LIMIT 1";
                    $template_result = mysqli_query($conn, $template_sql);
                    
                    if ($template_result && $template = mysqli_fetch_assoc($template_result)) {
                        $subject = $template['subject'];
                        $body = $template['body'];
                        
                        // Replace placeholders with actual data
                        $body = str_replace('{{first_name}}', $first_name, $body);
                        $body = str_replace('{{last_name}}', $last_name, $body);
                        $body = str_replace('{{email}}', $email, $body);
                        $body = str_replace('{{phone}}', $phone, $body);
                        $body = str_replace('{{interest}}', $interest, $body);
                    }
                } catch (Exception $e) {
                    // If there's an error (like table doesn't exist), we'll use the default template defined above
                    // No need to do anything here as we already set default values
                }
                
                // Send the email
                $email_result = send_email($email, $subject, $body);
                
                try {
                    // Try to log the email if the table exists
                    $log_status = $email_result['success'] ? 'sent' : 'failed';
                    $log_error = $email_result['success'] ? NULL : $email_result['message'];
                    
                    $log_sql = "INSERT INTO email_logs (recipient, subject, status, error_message, sent_at) 
                               VALUES (?, ?, ?, ?, NOW())";
                    $log_stmt = mysqli_prepare($conn, $log_sql);
                    mysqli_stmt_bind_param($log_stmt, "ssss", $email, $subject, $log_status, $log_error);
                    mysqli_stmt_execute($log_stmt);
                    mysqli_stmt_close($log_stmt);
                } catch (Exception $e) {
                    // If there's an error (like table doesn't exist), we'll just continue without logging
                }
                
                // Set success message
                $success_message = "Thank you for your interest! Your information has been submitted successfully. One of our representatives will contact you shortly.";
                
                // Clear form fields after successful submission
                $first_name = $last_name = $email = $phone = $interest = $source = '';
            } else {
                $error_message = "Oops! Something went wrong. Please try again later.";
            }
            
            // Close statement
            mysqli_stmt_close($stmt);
        } else {
            $error_message = "Database error. Please try again later.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Lead Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
        }
        .form-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .input-field {
            transition: border-color 0.3s;
        }
        .input-field:focus {
            border-color: #1E3E62;
            outline: none;
            box-shadow: 0 0 0 3px rgba(30, 62, 98, 0.1);
        }
        .submit-button {
            background-color: #1E3E62;
            transition: background-color 0.3s, transform 0.2s;
        }
        .submit-button:hover {
            background-color: #0B192C;
            transform: translateY(-2px);
        }
        .error-text {
            color: #dc2626;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Contact Us</h1>
                <p class="text-gray-600">Fill out the form below and we'll get back to you as soon as possible.</p>
            </div>
            
            <!-- Success Message -->
            <?php if (!empty($success_message)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline"><?php echo $success_message; ?></span>
                <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                    <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </button>
            </div>
            <?php endif; ?>
            
            <!-- Error Message -->
            <?php if (!empty($error_message)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline"><?php echo $error_message; ?></span>
                <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                    <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </button>
            </div>
            <?php endif; ?>
            
            <!-- Contact Form -->
            <div class="form-container p-8">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" class="space-y-6">
                    <!-- Name Fields (First and Last) -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                            <input type="text" id="first_name" name="first_name" value="<?php echo $first_name; ?>" class="input-field w-full px-4 py-2 border border-gray-300 rounded-md" required>
                            <?php if (!empty($first_name_err)): ?>
                                <p class="error-text mt-1"><?php echo $first_name_err; ?></p>
                            <?php endif; ?>
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                            <input type="text" id="last_name" name="last_name" value="<?php echo $last_name; ?>" class="input-field w-full px-4 py-2 border border-gray-300 rounded-md" required>
                            <?php if (!empty($last_name_err)): ?>
                                <p class="error-text mt-1"><?php echo $last_name_err; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email <span class="text-red-500">*</span></label>
                            <input type="email" id="email" name="email" value="<?php echo $email; ?>" class="input-field w-full px-4 py-2 border border-gray-300 rounded-md" required>
                            <?php if (!empty($email_err)): ?>
                                <p class="error-text mt-1"><?php echo $email_err; ?></p>
                            <?php endif; ?>
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone <span class="text-red-500">*</span></label>
                            <input type="tel" id="phone" name="phone" value="<?php echo $phone; ?>" class="input-field w-full px-4 py-2 border border-gray-300 rounded-md" required>
                            <?php if (!empty($phone_err)): ?>
                                <p class="error-text mt-1"><?php echo $phone_err; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    

                    
                    <!-- Interest -->
                    <div>
                        <label for="interest" class="block text-sm font-medium text-gray-700 mb-1">What are you interested in? <span class="text-red-500">*</span></label>
                        <select id="interest" name="interest" class="input-field w-full px-4 py-2 border border-gray-300 rounded-md" required>
                            <option value="" <?php echo empty($interest) ? 'selected' : ''; ?>>Select your interest</option>
                            <option value="Basic Package" <?php echo $interest == 'Basic Package' ? 'selected' : ''; ?>>Basic Package</option>
                            <option value="Premium Package" <?php echo $interest == 'Premium Package' ? 'selected' : ''; ?>>Premium Package</option>
                            <option value="Enterprise Package" <?php echo $interest == 'Enterprise Package' ? 'selected' : ''; ?>>Enterprise Package</option>
                            <option value="Custom Solution" <?php echo $interest == 'Custom Solution' ? 'selected' : ''; ?>>Custom Solution</option>
                            <option value="General Inquiry" <?php echo $interest == 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                        </select>
                        <?php if (!empty($interest_err)): ?>
                            <p class="error-text mt-1"><?php echo $interest_err; ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Source (Hidden, default to Web Form) -->
                    <input type="hidden" name="source" value="Web Form">
                    
                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="submit" class="submit-button w-full text-white font-medium py-3 px-4 rounded-md">
                            Submit Inquiry
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Back to Home Link -->
            <div class="text-center mt-6">
                <a href="LMS/index.html" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Home
                </a>
            </div>
        </div>
    </div>
</body>
</html>