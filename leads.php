<?php

$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_leads = isset($_POST['selected_leads']) ? $_POST['selected_leads'] : [];

    if (!empty($selected_leads) && !empty($action)) {
        $lead_ids = array_map('intval', $selected_leads);
        $placeholders = str_repeat('?,', count($lead_ids) - 1) . '?';

        if ($action === 'delete') {
            $delete_sql = "DELETE FROM leads WHERE id IN ($placeholders)";
            $stmt = mysqli_prepare($conn, $delete_sql);
            mysqli_stmt_bind_param($stmt, str_repeat('i', count($lead_ids)), ...$lead_ids);

            if (mysqli_stmt_execute($stmt)) {
                $success_message = count($lead_ids) . " lead(s) deleted successfully.";
            } else {
                $error_message = "Error deleting leads: " . mysqli_error($conn);
            }
        } elseif ($action === 'convert') {
            // Convert leads to customers
            $success_count = 0;
            $error_count = 0;
            $error_messages = [];

            foreach ($lead_ids as $lead_id) {
                $conversion_result = convert_lead_to_customer($conn, $lead_id);
                if ($conversion_result['success']) {
                    $success_count++;
                } else {
                    $error_count++;
                    $error_messages[] = "Lead ID $lead_id: " . $conversion_result['message'];
                }
            }

            if ($success_count > 0) {
                $success_message = "$success_count lead(s) converted to customers successfully.";
                if ($error_count > 0) {
                    $success_message .= " $error_count conversion(s) failed.";
                }
            } else {
                $error_message = "Error converting leads: " . implode('; ', $error_messages);
            }
        } elseif ($action === 'assign' && isset($_POST['assign_to_user']) && !empty($_POST['assign_to_user'])) {
            $assign_to = intval($_POST['assign_to_user']);
            if ($assign_to > 0) {
                $assign_sql = "UPDATE leads SET assigned_to = ? WHERE id IN ($placeholders)";
                $stmt = mysqli_prepare($conn, $assign_sql);
                $params = array_merge([$assign_to], $lead_ids);
                mysqli_stmt_bind_param($stmt, 'i' . str_repeat('i', count($lead_ids)), ...$params);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($lead_ids) . " lead(s) assigned successfully.";
                    
                    // Get the sales rep details for email notification
                    $sales_rep_sql = "SELECT first_name, last_name, email FROM employees WHERE id = ?";
                    $sales_rep_stmt = mysqli_prepare($conn, $sales_rep_sql);
                    mysqli_stmt_bind_param($sales_rep_stmt, "i", $assign_to);
                    mysqli_stmt_execute($sales_rep_stmt);
                    $sales_rep_result = mysqli_stmt_get_result($sales_rep_stmt);
                    $sales_rep = mysqli_fetch_assoc($sales_rep_result);
                    
                    // Create notification for the sales rep
                    $notification_title = "Leads Assigned";
                    $notification_message = count($lead_ids) . " lead(s) have been assigned to you.";
                    
                    $notify_sql = "INSERT INTO notifications (user_id, title, message, type, related_to, related_id, created_at) 
                                  VALUES (?, ?, ?, 'lead_assigned', 'contacts', NULL, NOW())";
                    $notify_stmt = mysqli_prepare($conn, $notify_sql);
                    mysqli_stmt_bind_param($notify_stmt, "iss", $assign_to, $notification_title, $notification_message);
                    mysqli_stmt_execute($notify_stmt);
                    
                    // Send email notifications to each lead
                    foreach ($lead_ids as $lead_id) {
                        // Get lead details
                        $lead_sql = "SELECT first_name, last_name, email FROM contacts WHERE id = ?";
                        $lead_stmt = mysqli_prepare($conn, $lead_sql);
                        mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                        mysqli_stmt_execute($lead_stmt);
                        $lead_result = mysqli_stmt_get_result($lead_stmt);
                        $lead = mysqli_fetch_assoc($lead_result);
                        
                        // Get the lead_assigned email template
                        $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_assigned' LIMIT 1";
                        $template_result = mysqli_query($conn, $template_sql);
                        
                        if ($template = mysqli_fetch_assoc($template_result)) {
                            $subject = $template['subject'];
                            $body = $template['body'];
                            
                            // Replace placeholders with actual data
                            $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                            $body = str_replace('{{sales_rep_name}}', $sales_rep['first_name'] . ' ' . $sales_rep['last_name'], $body);
                            $body = str_replace('{{sales_rep_email}}', $sales_rep['email'], $body);
                            
                            // Send the email
                            $email_result = send_email($lead['email'], $subject, $body);
                            
                            // Log the email
                            $log_status = $email_result['success'] ? 'sent' : 'failed';
                            $log_error = $email_result['success'] ? NULL : $email_result['message'];
                            
                            $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                                       VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_assigned' LIMIT 1), ?, ?, NOW())";
                            $log_stmt = mysqli_prepare($conn, $log_sql);
                            mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                            mysqli_stmt_execute($log_stmt);
                        }
                    }
                } else {
                    $error_message = "Error assigning leads: " . mysqli_error($conn);
                }
            } else {
                $error_message = "Please select a valid user to assign leads to.";
            }
        } elseif ($action === 'followup') {
            // Send follow-up emails to selected leads
            $success_count = 0;
            $error_count = 0;
            
            foreach ($lead_ids as $lead_id) {
                // Get lead details
                $lead_sql = "SELECT l.first_name, l.last_name, l.email, l.assigned_to,
                            CONCAT(e.first_name, ' ', e.last_name) as sales_rep_name
                            FROM leads l
                            LEFT JOIN employees e ON l.assigned_to = e.id
                            WHERE l.id = ?";
                $lead_stmt = mysqli_prepare($conn, $lead_sql);
                mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                mysqli_stmt_execute($lead_stmt);
                $lead_result = mysqli_stmt_get_result($lead_stmt);
                $lead = mysqli_fetch_assoc($lead_result);
                
                // Get the lead_followup email template
                $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_followup' LIMIT 1";
                $template_result = mysqli_query($conn, $template_sql);
                
                if ($template = mysqli_fetch_assoc($template_result)) {
                    $subject = $template['subject'];
                    $body = $template['body'];
                    
                    // Replace placeholders with actual data
                    $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                    $body = str_replace('{{sales_rep_name}}', $lead['sales_rep_name'] ?? 'our team', $body);
                    
                    // Send the email
                    $email_result = send_email($lead['email'], $subject, $body);
                    
                    // Log the email
                    $log_status = $email_result['success'] ? 'sent' : 'failed';
                    $log_error = $email_result['success'] ? NULL : $email_result['message'];
                    
                    $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                               VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_followup' LIMIT 1), ?, ?, NOW())";
                    $log_stmt = mysqli_prepare($conn, $log_sql);
                    mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                    mysqli_stmt_execute($log_stmt);
                    
                    if ($email_result['success']) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                }
            }
            
            if ($success_count > 0) {
                $success_message = "Follow-up emails sent to $success_count lead(s) successfully.";
                if ($error_count > 0) {
                    $success_message .= " $error_count email(s) failed to send.";
                }
            } else {
                $error_message = "Error sending follow-up emails.";
            }
        }
    } else {
        if (empty($selected_leads)) {
            $error_message = "Please select at least one lead.";
        } elseif (empty($action)) {
            $error_message = "Please choose an action.";
        } else {
            $error_message = "Please select leads and choose an action.";
        }
    }
}

// Get filter parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build filtered query conditions
$where_conditions = [];
$params = [];
$param_types = '';

// Filter leads based on status and other criteria
if ($is_sales_rep) {
    $where_conditions[] = "leads.assigned_to = ?";
    $params[] = $sales_rep_id;
    $param_types .= 'i';
}

if (!empty($filter)) {
    // Handle different filter types
    switch ($filter) {
        case 'new':
            $where_conditions[] = "leads.status = 'new'";
            break;
        case 'contacted':
            $where_conditions[] = "leads.status = 'contacted'";
            break;
        case 'qualified':
            $where_conditions[] = "leads.status = 'qualified'";
            break;
        case 'assigned':
            $where_conditions[] = "leads.assigned_to IS NOT NULL";
            break;
        case 'unassigned':
            $where_conditions[] = "leads.assigned_to IS NULL";
            break;
        case 'recent':
            $where_conditions[] = "leads.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(leads.first_name LIKE ? OR leads.last_name LIKE ? OR leads.email LIKE ? OR leads.phone LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'ssss';
}

$where_clause = !empty($where_conditions) ? " WHERE " . implode(" AND ", $where_conditions) : "";

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM leads" . $where_clause;
if (!empty($params)) {
    $count_stmt = mysqli_prepare($conn, $count_sql);
    mysqli_stmt_bind_param($count_stmt, $param_types, ...$params);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
} else {
    $count_result = mysqli_query($conn, $count_sql);
}
$total_records = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_records / $records_per_page);

// Build main query with pagination
$main_where_conditions = [];
foreach ($where_conditions as $condition) {
    $main_where_conditions[] = str_replace(
        ['leads.assigned_to', 'leads.first_name', 'leads.last_name', 'leads.email', 'leads.phone'],
        ['l.assigned_to', 'l.first_name', 'l.last_name', 'l.email', 'l.phone'],
        $condition
    );
}
$main_where_clause = !empty($main_where_conditions) ? " WHERE " . implode(" AND ", $main_where_conditions) : "";

$sql = "SELECT l.*,
        CONCAT(l.first_name, ' ', l.last_name) as full_name,
        CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name,
        CONCAT(u2.first_name, ' ', u2.last_name) as created_by_name
        FROM leads l
        LEFT JOIN employees u ON l.assigned_to = u.id
        LEFT JOIN employees u2 ON l.created_by = u2.id" .
        $main_where_clause .
        " ORDER BY l.created_at DESC LIMIT ?, ?";

// Add pagination parameters
$params[] = $offset;
$params[] = $records_per_page;
$param_types .= 'ii';

// Execute the query
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, $param_types, ...$params);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get all employees for assignment dropdown
$employees_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role IN ('sales_rep', 'manager')";
$employees_result = mysqli_query($conn, $employees_sql);
$employees = [];
while ($row = mysqli_fetch_assoc($employees_result)) {
    $employees[] = $row;
}

// Get statistics from leads table
if ($is_sales_rep) {
    // Stats for Sales Rep
    $stats_sql = "SELECT
                 COUNT(*) as total_leads,
                 SUM(CASE WHEN leads.status = 'new' THEN 1 ELSE 0 END) as new_leads,
                 SUM(CASE WHEN leads.status = 'contacted' THEN 1 ELSE 0 END) as contacted_leads,
                 SUM(CASE WHEN leads.status = 'qualified' THEN 1 ELSE 0 END) as qualified_leads
                 FROM leads WHERE leads.assigned_to = ?";
    $stmt = mysqli_prepare($conn, $stats_sql);
    mysqli_stmt_bind_param($stmt, 'i', $sales_rep_id);
    mysqli_stmt_execute($stmt);
    $stats = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt));

    // Set assigned/unassigned for sales rep (all their leads are assigned to them)
    $stats['assigned_leads'] = $stats['total_leads'];
    $stats['unassigned_leads'] = 0;
} else {
    // Stats for Admin/Manager
    $stats_sql = "SELECT
                 COUNT(*) as total_leads,
                 SUM(CASE WHEN leads.assigned_to IS NOT NULL THEN 1 ELSE 0 END) as assigned_leads,
                 SUM(CASE WHEN leads.assigned_to IS NULL THEN 1 ELSE 0 END) as unassigned_leads,
                 SUM(CASE WHEN leads.status = 'new' THEN 1 ELSE 0 END) as new_leads,
                 SUM(CASE WHEN leads.status = 'contacted' THEN 1 ELSE 0 END) as contacted_leads,
                 SUM(CASE WHEN leads.status = 'qualified' THEN 1 ELSE 0 END) as qualified_leads
                 FROM leads";
    $stats_result = mysqli_query($conn, $stats_sql);
    $stats = mysqli_fetch_assoc($stats_result);
}

// Include header
$page_title = "Leads Management";
include __DIR__ . '/includes/header.php';

// Add direct Tailwind CSS script for this page
echo '<script src="https://cdn.tailwindcss.com"></script>';
echo '<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: "#1E3E62",
                    secondary: "#FF6500",
                }
            }
        }
    }
</script>';
?>

<?php include __DIR__ . '/includes/navigation.php'; ?>
    
<div class="flex-grow p-4 md:p-8 ml-0 md:ml-60 mt-16">
        <div class="max-w-7xl mx-auto">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4 md:mb-0">
                    <?php echo $is_sales_rep ? "My Leads" : "Leads Management"; ?>
                </h1>
                
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="add_lead.php" class="inline-flex items-center justify-center bg-primary text-white py-2 px-4 rounded-md hover:bg-blue-800 transition-colors">
                        <i class="fas fa-plus mr-2"></i> Add New Lead
                    </a>
                    
                    <?php if (!$is_sales_rep): ?>
                    <a href="import_leads.php" class="inline-flex items-center justify-center bg-secondary text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors">
                        <i class="fas fa-file-import mr-2"></i> Import Leads
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
                    <h3 class="text-gray-500 text-sm font-medium">Total Leads</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['total_leads']; ?></p>
                </div>

                <?php if (!$is_sales_rep): ?>
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-indigo-500">
                    <h3 class="text-gray-500 text-sm font-medium">Assigned</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['assigned_leads']; ?></p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-red-500">
                    <h3 class="text-gray-500 text-sm font-medium">Unassigned</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['unassigned_leads']; ?></p>
                </div>
                <?php endif; ?>

                <!-- Status-based cards -->
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500">
                    <h3 class="text-gray-500 text-sm font-medium">New</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['new_leads'] ?? 0; ?></p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-yellow-500">
                    <h3 class="text-gray-500 text-sm font-medium">Contacted</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['contacted_leads'] ?? 0; ?></p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-purple-500">
                    <h3 class="text-gray-500 text-sm font-medium">Qualified</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['qualified_leads'] ?? 0; ?></p>
                </div>
            </div>
            
            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex flex-wrap gap-2">
                        <a href="?filter=" class="px-3 py-1 rounded-full text-sm <?php echo empty($filter) ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            All
                        </a>
                        <a href="?filter=new" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'new' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            New
                        </a>
                        <a href="?filter=contacted" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'contacted' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Contacted
                        </a>
                        <a href="?filter=qualified" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'qualified' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Qualified
                        </a>
                        <?php if (!$is_sales_rep): ?>
                        <a href="?filter=assigned" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'assigned' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Assigned
                        </a>
                        <a href="?filter=unassigned" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'unassigned' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Unassigned
                        </a>
                        <?php endif; ?>
                        <a href="?filter=recent" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'recent' ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Recent
                        </a>
                    </div>
                    
                    <form action="" method="get" class="flex items-center">
                        <input type="hidden" name="filter" value="<?php echo htmlspecialchars($filter); ?>">
                        <div class="relative">
                            <input 
                                type="text" 
                                name="search" 
                                placeholder="Search leads..." 
                                value="<?php echo htmlspecialchars($search); ?>"
                                class="border border-gray-300 rounded-md py-2 px-4 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php if (isset($success_message)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p><?php echo $success_message; ?></p>
            </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                <p><?php echo $error_message; ?></p>
            </div>
            <?php endif; ?>
            
            <!-- Leads Table -->
            <form action="" method="post">
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    <?php if (mysqli_num_rows($result) > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div class="flex items-center">
                                            <input type="checkbox" id="select-all" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label for="select-all" class="sr-only">Select All</label>
                                            Name
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Contact Info
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Assigned To
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Created
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <input type="checkbox" name="selected_leads[]" value="<?php echo $row['id']; ?>" class="lead-checkbox mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($row['full_name']); ?>
                                                </div>

                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            <a href="mailto:<?php echo htmlspecialchars($row['email']); ?>" class="hover:text-blue-600">
                                                <?php echo htmlspecialchars($row['email']); ?>
                                            </a>
                                        </div>
                                        <?php if (!empty($row['phone'])): ?>
                                        <div class="text-sm text-gray-500">
                                            <a href="tel:<?php echo htmlspecialchars($row['country_code'] . $row['phone']); ?>" class="hover:text-blue-600">
                                                <?php echo htmlspecialchars($row['country_code'] . ' ' . $row['phone']); ?>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_colors = [
                                            'new' => 'bg-blue-100 text-blue-800',
                                            'contacted' => 'bg-yellow-100 text-yellow-800',
                                            'qualified' => 'bg-green-100 text-green-800',
                                            'lost' => 'bg-red-100 text-red-800'
                                        ];
                                        $status_color = $status_colors[$row['status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_color; ?>">
                                            <?php echo ucfirst(htmlspecialchars($row['status'])); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if (!empty($row['assigned_to_name'])): ?>
                                            <?php echo htmlspecialchars($row['assigned_to_name']); ?>
                                        <?php else: ?>
                                            <span class="text-gray-400">Unassigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('M j, Y', strtotime($row['created_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="view_lead.php?id=<?php echo $row['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-3" title="View Lead">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit_lead.php?id=<?php echo $row['id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Edit Lead">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="convert_lead.php?id=<?php echo $row['id']; ?>" class="text-green-600 hover:text-green-900 mr-3" title="Convert to Customer">
                                            <i class="fas fa-user-check"></i>
                                        </a>
                                        <a href="javascript:void(0);" onclick="confirmDelete(<?php echo $row['id']; ?>)" class="text-red-600 hover:text-red-900" title="Delete Lead">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Bulk Actions -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-wrap gap-4 items-center">
                        <select name="bulk_action" class="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Bulk Actions</option>
                            <option value="convert">Convert to Customers</option>
                            <option value="delete">Delete Selected</option>
                            <option value="followup">Send Follow-up Email</option>
                            <option value="assign">Assign to Sales Rep</option>
                        </select>
                        
                        <select name="assign_to_user" class="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hidden" id="assign-to-select">
                            <option value="">Select Sales Rep</option>
                            <?php foreach ($employees as $employee): ?>
                            <option value="<?php echo $employee['id']; ?>"><?php echo htmlspecialchars($employee['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                        

                        
                        <button type="submit" class="bg-primary text-white py-2 px-4 rounded-md hover:bg-blue-800 transition-colors">
                            Apply
                        </button>
                        
                        <span class="text-sm text-gray-500">
                            <?php echo $total_records; ?> lead(s) found
                        </span>
                    </div>
                    
                    <?php else: ?>
                    <div class="p-6 text-center">
                        <p class="text-gray-500 mb-4">No leads found.</p>
                        <a href="add_lead.php" class="inline-flex items-center justify-center bg-primary text-white py-2 px-4 rounded-md hover:bg-blue-800 transition-colors">
                            <i class="fas fa-plus mr-2"></i> Add New Lead
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </form>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="flex justify-center mt-6">
                <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <?php else: ?>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                        <span class="sr-only">Previous</span>
                        <i class="fas fa-chevron-left"></i>
                    </span>
                    <?php endif; ?>
                    
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $start_page + 4);
                    
                    if ($end_page - $start_page < 4 && $start_page > 1) {
                        $start_page = max(1, $end_page - 4);
                    }
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <a href="?page=<?php echo $i; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 <?php echo $i === $page ? 'bg-blue-50 text-blue-600 z-10' : 'bg-white text-gray-500 hover:bg-gray-50'; ?> text-sm font-medium">
                        <?php echo $i; ?>
                    </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    <?php else: ?>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                        <span class="sr-only">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </span>
                    <?php endif; ?>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Select all checkbox functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.lead-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Show/hide additional fields based on bulk action selection
document.querySelector('select[name="bulk_action"]').addEventListener('change', function() {
    const assignSelect = document.getElementById('assign-to-select');
    
    // Hide all conditional selects first
    assignSelect.classList.add('hidden');
    
    // Show relevant select based on action
    if (this.value === 'assign') {
        assignSelect.classList.remove('hidden');
    }
});

// Confirm delete function
function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this lead? This action cannot be undone.')) {
        window.location.href = 'delete_lead.php?id=' + id;
    }
}
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>